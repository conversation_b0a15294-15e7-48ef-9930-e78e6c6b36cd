<?php $__env->startPush("css"); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="hero_section buyer_homePage">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                            <div class="banner_content">
                                <h1>Explore</h1>
                                <div class="projects_searchbar">
                                    <div class="txt_field">
                                        <input type="text" id="categorySearch" placeholder="Search for Projects by Category" class="form-control">
                                        <i class="fa-solid fa-magnifying-glass input_icon"></i>
                                    </div>









                                </div>
                                <h6>What services are you looking for today?</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="buyer_home_cards sp_explore_page">
        <div class="container">
            <div class="row custom_rowGap">
                <div class="col-md-12">
                    <div class="homepage_cards_top">
                        <div class="top_results"> <h5>Top Results</h5><span>1-20 of 8K results</span></div>
                        <div class="top_results jobs_search_categories"><h5>Sort by:</h5>
                            <select name="" id="">
                                <option value="newest">Newest</option>
                                <option value="oldest">Oldest</option>
                                <option value="monthago">1 Month Ago</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row custom_row" id="jobsContainer">
                    <?php $__empty_1 = true; $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div  class="col-md-12 job-item"
                              data-category="<?php echo e($job->category->name ?? ''); ?>"
                              data-subcategory="<?php echo e($job->subCategory->name ?? ''); ?>">
                        <div class="explore_card">
                            <div class="row custom_rowGap" >
                                <div class="col-md-12">
                                    <div class="explore_card_user">
                                        <div class="custom_categories">
                                            <a href="#!" class="">Title :  <?php echo e($job->project_title ?? '---'); ?> </a>
                                        </div>
                                        <div class="logo_and_name">
                                            <div class="logo_content_rating">
                                                <div class="img_container">
                                                        <img src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($job?->user?->profile?->pic??''); ?>" alt="">
                                                </div>
                                                    <h3><?php echo e($job?->user->name ?? '----'); ?></h3>
                                            </div>
                                            <div class="rating_star">
                                                <div class="seller_rating">
                                                    <span><i class="fa fa-star <?php if($job->user->ratingSum >= 1): ?> checked <?php endif; ?>"></i></span>
                                                    <span><i class="fa fa-star <?php if($job->user->ratingSum >= 2): ?> checked <?php endif; ?>"></i></span>
                                                    <span><i class="fa fa-star <?php if($job->user->ratingSum >= 3): ?> checked <?php endif; ?>"></i></span>
                                                    <span><i class="fa fa-star <?php if($job->user->ratingSum >= 4): ?> checked <?php endif; ?>"></i></span>
                                                    <span><i class="fa fa-star <?php if($job->user->ratingSum >= 5): ?> checked <?php endif; ?>"></i></span>
                                                    <h6><?php echo e($job->user->ratingSum); ?> Stars</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="card_info">
                                        <h5>Posted On: <span><?php echo e($job->created_at->format('d-m-y')??'---'); ?></span></h5>
                                        <h5>Location<span><?php echo e($job->city ??'---'); ?> / <?php echo e($job->state ??'---'); ?></span></h5>
                                        <h5>Project Budget:<span><?php echo e($job->project_budget_max ?? '0'); ?></span></h5>
                                        <h5>Project Number:<span><?php echo e($job->project_number ?? '---'); ?></span></h5>
                                    </div>
                                </div>
                                
                                    
                                        
                                    
                                
                                <div class="col-md-12">
                                    <div class="custom_categories">
                                        <a href="#!" class=""><?php echo e($job->category->name ?? '----'); ?> / <?php echo e($job->subCategory->name ?? '----'); ?></a>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <h5>Budget:</h5>
                                    <h3>$<?php echo e($job->project_budget_min??'0'); ?> - $<?php echo e($job->project_budget_max??'0'); ?></h3>
                                </div>

                                <?php if(!empty($job->jobQuestionAnswer)): ?>
                                    <?php $__currentLoopData = $job->jobQuestionAnswer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-12">
                                            <h5><?php echo e($element['question'] ?? '----'); ?></h5>
                                            <?php
                                                $value = is_object($element['value']) ? (array) $element['value'] : $element['value'];
                                            ?>
                                            <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $value)): ?>
                                                <a href="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" data-fancybox="gallery">
                                                    <div class="questionnaire_img">
                                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($value); ?>" style="max-width: 100%;">
                                                    </div>
                                                </a>
                                            <?php else: ?>
                                                <h6><?php echo e(is_string($value) ? $value : '----'); ?></h6>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>



















                                <div class="col-md-12">
                                    <h5>Photos</h5>
                                    <div class="explore_photos">
                                        <div class="row custom_row">
                                            <?php if(!empty($job->jobFiles)): ?>
                                                <?php $__currentLoopData = $job->jobFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                                        <?php
                                                            $fileUrl = $file->file ?? '---';
                                                            $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                            $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico', 'heic', 'heif', 'jfif', 'avif']);
                                                            $isVideo = in_array($extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv', 'mpeg', 'mpg', 'm4v', '3gp', '3g2']);
                                                        ?>
                                                        <?php if($isImage): ?>
                                                            <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                <div class="project_photos">
                                                                    <img src="<?php echo e($fileUrl); ?>" alt="Job File" class="img-fluid">
                                                                </div>
                                                            </a>
                                                        <?php elseif($isVideo): ?>
                                                            <div class="project_photos">
                                                                <a href="<?php echo e($fileUrl); ?>" data-fancybox="gallery">
                                                                    <div class="downloadable_video">
                                                                        <video controls preload="metadata" class="video-player">
                                                                            <source src="<?php echo e($fileUrl); ?>" type="video/<?php echo e($extension === 'mov' ? 'quicktime' : $extension); ?>">
                                                                            Your browser does not support this video format.
                                                                        </video>
                                                                    </div>
                                                                </a>
                                                                <a href="<?php echo e($fileUrl); ?>" download class="download_video">Download the video</a>
                                                            </div>
                                                        <?php else: ?>
                                                            <div class="project_photos">
                                                                <p>Unsupported file type: <?php echo e($extension); ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="view_project">
                                        <a href="<?php echo e(url('explore_view_project')); ?>/<?php echo e($job->id); ?>" class="btn btn_blue btn_has_icon">View Project <div class="btn_icon"> <i class="fa-solid fa-arrow-right"></i></div></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <span> No Jobs Available </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <div class="white-box">
        <div class="modal fade filter" id="filter" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog modal-dialog-centered " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title" id="exampleModalLabel1">Filter</h2>
                    </div>
                    <div class="modal-body">
                        <h5>Price Range</h5>
                        <div class="price-range-container">
                            <label for="priceRange" class="pricing_value"><div id="minPrice">0</div> <div id="maxPrice">1000</div></label>
                            <div class="slider-container">
                                <input type="range" id="priceRange" min="0" max="1000" value="0" step="10" >
                                <input type="range" id="maxPriceRange" min="0" max="1000" value="1000" step="10">
                            </div>
                        </div>
                        <h5>Project Type</h5>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one">
                                <label for="one">Basic</label>
                            </div>
                        </div>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one1">
                                <label for="one1">Extensive</label>
                            </div>
                        </div>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one2">
                                <label for="one2">Recurring Service</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn_transparent " data-dismiss="modal">Cancel</button>
                        <button type="Submit" class="btn btn_black btn_has_icon" >Apply <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></button>

                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });
    });
</script>
    <script>
        const priceRange = document.getElementById('priceRange');
        const maxPriceRange = document.getElementById('maxPriceRange');
        const minPriceLabel = document.getElementById('minPrice');
        const maxPriceLabel = document.getElementById('maxPrice');

        priceRange.addEventListener('input', updatePriceLabels);
        maxPriceRange.addEventListener('input', updatePriceLabels);

        function updatePriceLabels() {
            minPriceLabel.textContent = priceRange.value;
            maxPriceLabel.textContent = maxPriceRange.value;

            if (parseInt(priceRange.value) >= parseInt(maxPriceRange.value)) {
                priceRange.value = maxPriceRange.value;
            }

            if (parseInt(maxPriceRange.value) <= parseInt(priceRange.value)) {
                maxPriceRange.value = priceRange.value;
            }
        }

        $(document).ready(function () {
            // Handle keyup event on the search input
            $('#categorySearch').on('keyup', function () {
                let searchValue = $(this).val().toLowerCase(); // Get the input value and convert to lowercase
                // Loop through each job item
                $('.job-item').each(function () {
                    let category = $(this).data('category').toLowerCase(); // Get the category
                    let subcategory = $(this).data('subcategory').toLowerCase(); // Get the subcategory
                    // Check if the search value matches category or subcategory
                    if (category.includes(searchValue) || subcategory.includes(searchValue)) {
                        $(this).show(); // Show matching jobs
                    } else {
                        $(this).hide(); // Hide non-matching jobs
                    }
                });
            });
            // Optional: Handle search button click
            $('#searchBtn').on('click', function () {
                $('#categorySearch').trigger('keyup'); // Trigger the same functionality as typing
            });

//            $(document).on('change', '.jobs_search_categories select', function () {
//                let filterValue = $(this).val().toLowerCase();
//                let currentDate = new Date();
//                let jobItems = $('.job-item').toArray();
//
//                jobItems.sort(function (a, b) {
//                    let dateA = new Date($(a).find('.card_info h5 span').first().text().trim().split('-').reverse().join('-'));
//                    let dateB = new Date($(b).find('.card_info h5 span').first().text().trim().split('-').reverse().join('-'));
//
//                    if (filterValue === 'newest') {
//                        return dateB - dateA;
//                    } else if (filterValue === 'oldest') {
//                        return dateA - dateB;
//                    }
//                    return 0;
//                });
//
//                $(jobItems).each(function (index, job) {
//                    $(job).css("order", index);
//                });
//
//                $('.job-item').each(function () {
//                    let postedDateText = $(this).find('.card_info h5 span').first().text().trim();
//                    if (postedDateText === '---') {
//                        $(this).hide();
//                        return;
//                    }
//                    let postedDate = new Date(postedDateText.split('-').reverse().join('-'));
//                    let showJob = false;
//
//                    if (filterValue === 'newest' || filterValue === 'oldest') {
//                        showJob = true;
//                    } else if (filterValue === 'monthago') {
//                        let oneMonthAgo = new Date();
//                        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
//                        showJob = postedDate >= oneMonthAgo && postedDate < currentDate;
//                    }
//
//                    $(this).toggle(showJob);
//                });
//            });

            $(document).ready(function () {
                $(".jobs_search_categories select").on("change", function () {
                    let selectedOption = $(this).val();
                    let currentDate = new Date();
                    let oneMonthAgo = new Date();
                    oneMonthAgo.setMonth(currentDate.getMonth() - 1);

                    const jobItems = $(".job-item").map(function() {
                        const $item = $(this);
                        const dateText = $item.find(".card_info h5:first span").text().trim();

                        // Parse date in format "d-m-y" (like "15-03-25" for March 15, 2025)
                        const [day, month, year] = dateText.split("-").map(Number);
                        const postedDate = new Date(2000 + year, month - 1, day);

                        return {
                            element: $item,
                            date: postedDate,
                            dateText: dateText
                        };
                    }).get();

                    if (selectedOption === "newest") {
                        // Newest first (descending)
                        jobItems.sort((a, b) => b.date - a.date);
                    } else if (selectedOption === "oldest") {
                        // Oldest first (ascending)
                        jobItems.sort((a, b) => a.date - b.date);
                    } else if (selectedOption === "monthago") {
                        // Filter for jobs from exactly one month ago
                        const filteredItems = jobItems.filter(job => {
                            return job.date.getMonth() === oneMonthAgo.getMonth() &&
                                job.date.getFullYear() === oneMonthAgo.getFullYear();
                        });

                        // Replace the array with filtered items
                        jobItems.length = 0;
                        jobItems.push(...filteredItems);
                    }

                    // Reorder the DOM elements
                    const $container = $("#jobsContainer");
                    $container.empty(); // Clear the container

                    // Append sorted items
                    jobItems.forEach(job => {
                        $container.append(job.element);
                    });
                });
            });
        });

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall\resources\views/website/serviceProvider/explore.blade.php ENDPATH**/ ?>