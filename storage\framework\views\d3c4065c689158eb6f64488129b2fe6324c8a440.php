                

                            <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                   <li class="contacts" style="cursor: pointer;">
                                    <a class="chat-box-toggle user_profile_msg" data-project="<?php echo e($user->getItem->project_title??''); ?>" data-show_group="<?php echo e($user->show_group); ?>" <?php if(isset($user->getMilestone->status)): ?> data-dispute-decision="<?php echo e($user->getMilestone->decision); ?>" data-dispute-released="<?php echo e($user->disputeMilestone->released ?? 0); ?>" data-dispute-id="<?php echo e($user->getMilestone->id ?? 0); ?>"  data-miles-stone-id="<?php echo e($user->getMilestone->disputed_milestone_id ?? 0); ?>" data-milestone-status="<?php echo e($user->getMilestone->status); ?>" data-req_staff="<?php echo e($user->req_staff); ?>"data-self_resolved="<?php echo e($user->self_resolved); ?>" data-solve_by_buyer="<?php echo e($user->getMilestone->solve_by_buyer); ?>" data-solve_by_seller="<?php echo e($user->getMilestone->solve_by_seller); ?>"  <?php endif; ?>  data-assign_staff_id="<?php echo e($user->getItem->assign_staff_id ?? 0); ?>"data-milestone_id="<?php echo e($user->milestone_id); ?>" data-user-name="<?php if(Auth::id() == $user->sender_id): ?> <?php echo e($user->getReceiver->name??''); ?> <?php else: ?> <?php echo e($user->getSender->name??''); ?> <?php endif; ?>" data-user-id="<?php if(Auth::id() == $user->sender_id): ?> <?php echo e(base64_encode(strtr($user->getReceiver->id, '._-', '+/='))??''); ?> <?php else: ?> <?php echo e(base64_encode(strtr($user->getSender->id, '._-', '+/='))??''); ?> <?php endif; ?>" data-sender="<?php echo e($user->sender_id); ?>" data-image="<?php if(Auth::id() == $user->sender_id): ?> <?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($user->getReceiver->profile->pic??''); ?>  <?php else: ?> <?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($user->getSender->profile->pic??''); ?>  <?php endif; ?>" data-item="<?php echo e($user->admin_id); ?>" data-id="<?php echo e($user->group_id); ?>" data-user="<?php if(Auth::id() == $user->sender_id): ?> <?php echo e($user->getReceiver->name); ?> <?php else: ?> <?php echo e($user->getSender->name); ?>  <?php endif; ?>" data-group="0">
                                        <div class="user_img">
                                        <img <?php if(Auth::id() == $user->sender_id): ?> src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($user->getReceiver->profile->pic??''); ?>"  <?php else: ?> src="<?php echo e(asset('storage')); ?>/uploads/users/<?php echo e($user->getSender->profile->pic??''); ?>"  <?php endif; ?>  class="avatar">
                                        </div>
                                        <div class="user_msg">
                                       <h6> <?php if(Auth::id() == $user->sender_id): ?> <?php echo e($user->getReceiver->name??''); ?> <?php else: ?> <?php echo e($user->getSender->name??''); ?> <?php endif; ?> </h6>
                                                <p><?php echo e($user->getItem->project_title??''); ?></p>
                                            
                                       
                                        </div>
                                  <?php   $Message_user = App\MessageViewed::where('group_id',$user->group_id)->where('receiver',Auth::id())->where('viewed',0)->count(); ?>
                                <?php if($Message_user > 0): ?>
                                <span style="background-color: red;" class="badge badge-xs badge-danger"><?php echo e($Message_user); ?></span>
                                <?php endif; ?>
                                </a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                
<?php /**PATH C:\xampp\htdocs\mrdoall\resources\views/website/ajax/get_side_bar_user.blade.php ENDPATH**/ ?>