<?php $__env->startPush("css"); ?>
<link rel="stylesheet" href="<?php echo e(asset('plugins/components/dropify/dist/css/dropify.min.css')); ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" />

<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="hero_section buyer_homePage">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_banner custom_height">
                        <video autoplay muted loop playsinline webkit-playsinline>
                            <source src="<?php echo e(asset('website')); ?>/assets/images/website_video.mp4" type="video/mp4">
                        </video>
                        <div class="background_banner">
                            <div class="banner_content">
                                <h1>Explore</h1>
                                <div class="projects_searchbar">
                                    <div class="txt_field">
                                        <input type="text" id="categorySearch" placeholder="Search for Projects" class="form-control">
                                        <i class="fa-solid fa-magnifying-glass input_icon"></i>
                                    </div>
                                </div>
                                <h6>What services are you looking for today?</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="buyer_home_cards expertise_cards">
        <div class="container">
            <div class="row custom_rowGap" id="categoryList">

                <?php $__currentLoopData = $category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-4 col-sm-4 col-6 custom_column show_stepper_form"
                         data-id="<?php echo e($element->id); ?>"
                         data-child-category="<?php echo e(implode(',', $element?->jobSubCategory?->pluck('name')->toArray())); ?>"
                         data-visit-required="<?php echo e(json_encode($element?->jobSubCategory?->map(fn($subCategory) => ['id' => $subCategory->id, 'visit_required' => $subCategory->visit_required]))); ?>"
                         data-name="<?php echo e($element->name); ?>">

                        <div class="site_visiting custom_card">
                            <div class="blogs_icon">
                                <span><i class="fa-solid fa-house-user"></i></span>
                            </div>
                            <h4><?php echo e($element->name); ?></h4>
                            <p><?php echo e($element->description); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <div class="white-box">
        <div class="modal fade filter" id="filter" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog modal-dialog-centered " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title" id="exampleModalLabel1">Filter</h2>
                    </div>
                    <div class="modal-body">
                        <h5>Price Range</h5>
                        <div class="price-range-container">
                            <label for="priceRange" class="pricing_value"><div id="minPrice">0</div> <div id="maxPrice">1000</div></label>
                            <div class="slider-container">
                                <input type="range" id="priceRange" min="0" max="1000" value="0" step="10" >
                                <input type="range" id="maxPriceRange" min="0" max="1000" value="1000" step="10">
                            </div>
                        </div>
                        <h5>Project Type</h5>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one">
                                <label for="one">Basic</label>
                            </div>
                        </div>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one1">
                                <label for="one1">Extensive</label>
                            </div>
                        </div>
                        <div class="txt_field">
                            <div class="form-group">
                                <input type="checkbox" id="one2">
                                <label for="one2">Recurring Service</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn_transparent " data-dismiss="modal">Cancel</button>
                        <button type="Submit" class="btn btn_black btn_has_icon" >Apply <div class="btn_icon"><i class="fa-solid fa-arrow-right"></i></div></button>

                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade custom_modal" id="stepper_form" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal_img">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/footer_logo.png">
                    </div>
                    <div class="modal_btn">
                        <a href="#!" class="btn btn_transparent" ><i class="fa-solid fa-arrow-left"></i>Back</a>
                        <button type="button" class="btn btn_black" id="next-btn"><i class="fa-solid fa-arrow-right"></i>Next</button>
                        <a href="#!" class="btn btn_black" data-bs-dismiss="modal" aria-label="Close">Close</a>
                    </div>
                </div>
                <div class="form_steps">
                    <span class ="custom_steps" id ="custom_steps-1"></span>
                    <span class ="custom_steps" id ="custom_steps-2"></span>
                    <span class ="custom_steps" id ="custom_steps-3"></span>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="custom_step_form post_project_section">
                                <form id="jobForm" action="<?php echo e(route('store_job_question')); ?>" method="POST" enctype="multipart/form-data" class="submit_post">
                                    <?php echo csrf_field(); ?>
                                    <div class="job_posting_form">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-12 custom_column_height">
                                                <div class="progress_step" >
                                                     <div class ="step" id ="step-1">
                                                        <h5>Choose a Job</h5>
                                                        <span class="steps_number" id="step_number-1">01</span>

                                                        <span class="check" id="check-1"><i class="fa-solid fa-check"></i></span>
                                                    </div>
                                                    <div class ="step" id ="step-2">
                                                        <h5>Fill the Requirements</h5>
                                                        <span class="steps_number" id="step_number-2">02</span>
                                                        <span class="check" id="check-2"><i class="fa-solid fa-check"></i></span>
                                                    </div>
                                                    <div class ="step" id ="step-3">
                                                        <h5>Post a Project</h5>
                                                        <span class="steps_number" id="step_number-3">03</span>
                                                        <span class="check" id="check-3"><i class="fa-solid fa-check"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-9 col-md-12">
                                                <div class="custom_tabs">
                                                    <div class="tab " id="tab-1">
                                                        <div class="custom_form_radio" id="show_all_subcategory_div">
                                                        </div>
                                                    </div>
                                                    <div class="tab" id = "tab-2">
                                                        <div class="row" id="job_qustion_div">
                                                        </div>
                                                    </div>
                                                    <div class="tab" id="tab-3">
                                                        <div class="post_project_content">
                                                            <div class="row">
                                                                <div class="col-md-12"><h5>Project Budget Overview</h5></div>
                                                                <div class="col-md-6 col-sm-6">
                                                                    <div class="single_field">
                                                                        <div class="txt_field">
                                                                            <label>What is the maximum amount you’d like to spend on this project?</label>
                                                                            <input type="text" name="project_budget_min"  placeholder="Enter Amount Here..." class="form-control project_budget_min" required id="budgetInput">
                                                                            <input type="hidden" name="category_id" id="category_id" placeholder="Enter Amount Here..." class="form-control" required>
                                                                            <i class="fa-solid fa-dollar-sign dollor_icon"></i>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="col-md-12"><h5>Date & Time for Staff Site Visit</h5></div>
                                                                <div class="col-md-6 col-sm-6">
                                                                    <div class="single_field">
                                                                        <div class="txt_field">
                                                                            <label>What is the visit date?</label>
                                                                            <div id="" class="date datepicker" data-date-format="mm-dd-yyyy">
                                                                                <input class="form-control" type="text" placeholder="DD/MM/YYYY" name="visit_date" required/>
                                                                                <span class="input-group-addon"><i class="fa-solid fa-calendar"></i></span>
                                                                            </div>
                                                                            
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 col-sm-6">
                                                                    <div class="single_field">
                                                                        <div class="txt_field">
                                                                            <label>What's the visit time?</label>
                                                                            <select class="form-control form-select" name="visit_time" aria-label="Default select example">
                                                                                <option selected disabled>Select Visit Time</option>
                                                                                <option value="8.00 am - 12.00 pm">8.00 am - 12.00 pm</option>
                                                                                <option value="12.00 pm - 4.00 pm">12.00 pm - 4.00 pm</option>
                                                                                <option value="4.00 pm - 8.00 pm">4.00 pm - 8.00 pm</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 col-sm-6">
                                                                    <div class="custom_address">
                                                                        <div class="txt_field">
                                                                            <label>Service Location</label>
                                                                            <div class="check_address">
                                                                                <input type="checkbox" id="" value="1" name="profile_address" checked required>
                                                                                <input type="text" class="form-control" value="<?php echo e(auth()->user()->profile->state??''); ?>" name="personal_address" required readonly="">
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="another_location">
                                                                        <div class="txt_field">
                                                                            <div class="check_address">
                                                                                <input type="checkbox" id="custom_address_checkbox" value="1" name="custom_address_checkbox">
                                                                                <input type="text" class="form-control" id="custom_address" placeholder="Enter Address" name="custom_address">
                                                                                <input type="hidden" class="form-control" id="custom_state" name="custom_state" >
                                                                                <input type="hidden" class="form-control" id="custom_city" name="custom_city" >
                                                                                <input type="hidden" class="form-control" id="custom_postal" name="custom_postal" >
                                                                                <input type="hidden" class="form-control" id="custom_country" name="custom_country" >
                                                                                <input type="hidden" class="form-control" id="custom_lng" name="custom_lng" >
                                                                                <input type="hidden" class="form-control" id="custom_lat" name="custom_lat" >
                                                                            </div>
                                                                        </div>
                                                                        <div class="remove_address">
                                                                            <button type="button" class="btn btn_black"><i class="fa-solid fa-trash"></i></button>
                                                                        </div>
                                                                    </div>
                                                                    <div class="add_another_address">
                                                                        <button type="button" class="btn btn_black">Need Service at a Different Address ?</button>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-12">
                                                                    <h5>Add Images/ Videos</h5>
                                                                    <div id="" class="custom_file_upload">
                                                                        <input type="hidden" id="cachedFiles" name="cachedFiles" value="[]" >
                                                                        <div id="fileContainer"></div>
                                                                        <div class="append_type_wrapper">
                                                                            <div class="append_type_file">
                                                                                <input type="file" class="file-input"  accept="*" required />
                                                                                <a class="image_icon" href="#!"><i class="fa-solid fa-image"></i></a>
                                                                                <span class="file_size_error" style="color: red;"></span>
                                                                                <button class="close-btn append_img_div_remove" type="button"><i class="fa-solid fa-close"></i></button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="new_custom_div"></div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>
<?php $__env->startPush("js"); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/resumablejs@1.1.0/resumable.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>
<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });
    });
</script>

    <script>
        document.getElementById("budgetInput").addEventListener("input", function (e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        $(document).ready(function () {

            $('.append_type_file .append_img_div_remove').hide();
            $(document).ready(function () {

        //COMMENT BY UNAIZ
        
        
        
        
        
        

        
        
        
        
        

        
        
        

        
        
        
        
        
        
        
        
        
        

        
        

        
        
        
        
        
        
        
        
        
        
        

        

        
        
        
        
        
        

        
        
        
        
        

        
        
        
        
        

        
        
        
        

        
        
        

        
        
        

        
        
        
        
        
        
        

        
        
        

        
        

        
        
        
        
        
        
        
        
        
        
        

        
        
        
        
        
        
        
        

        
        
        
        
        

        
        
        
        

        
        
        
        
        
        
        


                // old modify
                var fileUrls = []; // Array to store file URLs
                var resumable; // Declare global variable for resumable instance
                var ongoingUploads = 0;

                $(document).on("input",'.append_type_file .file-input',function() {
                    var fileInput = $(this);
                    // Check if a file is selected
                    if (fileInput.val() != '') {
                        fileInput.closest('.append_type_file').find('.append_img_div_remove').show();
                        $('#next-btn').attr('disabled', 'disabled');

                        var container = fileInput.closest('.custom_file_upload');
                        // Check if progress bar already exists for this file
                        if (fileInput.closest('.append_type_wrapper').find('.progressWrapper').length === 0) {
                            // Add a progress bar for this file
                            var progressWrapper = $('<div class="progressWrapper"></div>');
                            var progressBar = $('<progress class="fileProgress" value="0" max="100" style="width: 100%;"></progress>');
                            var progressText = $('<span class="progressText">0%</span>');

                            progressWrapper.append(progressBar).append(progressText);
                            fileInput.closest('.append_type_wrapper').append(progressWrapper);
                        }

                        // var loader = $('<div class="loader"></div>');
                        // fileInput.closest('.append_type_wrapper').append(loader);

                        // Create a new file input dynamically for the next file
                        container.append(`
                    <div class="append_type_wrapper">
                        <div class="append_type_file">
                                    <input type="file" class="file-input" accept="*" required />
                            <a href="#!" class="image_icon">
                                <i class="fa-solid fa-image"></i>
                            </a>
                            <button type="button" class="close-btn append_img_div_remove" style="display: none">
                                <i class="fa-solid fa-close"></i>
                            </button>
                        </div>
                    </div>
                        `);
                        // Now use Resumable.js to upload the file
                        var file = fileInput[0].files[0];
                        if (!file) return;
                        ongoingUploads++;

                        resumable = new Resumable({
                            target: '<?php echo e(route('upload_files_aws')); ?>',
                            query: {_token: '<?php echo e(csrf_token()); ?>'},
                            chunkSize: 2 * 1024 * 1024, // 2MB chunks
                            headers: {
                                'Accept': 'application/json'
                            },
                            testChunks: false,
                            throttleProgressCallbacks: 1,
                        });

                        resumable.addFile(file);

                        resumable.on('fileAdded', function (file) {
                            console.log('File added: ', file);
                            $('.pre_loader').show();
                            resumable.upload();
                        });

                        resumable.on('fileProgress', function (file) {
                            var progress = Math.floor(file.progress() * 100);
                            updateProgress(file, progress);
                        });
                        resumable.on('fileSuccess', function (file, response) {
                            ongoingUploads--;
                            if (ongoingUploads === 0) {
                                $('#next-btn').removeAttr('disabled');
                            }

                            $('.pre_loader').hide();
                            try {

                                response = JSON.parse(response);
                                console.log('File uploaded successfully:', response);

                                fileUrls.push(response.files[0].fileUrl);

                                $('#cachedFiles').val(JSON.stringify(fileUrls));

                                console.log('Cached Files:', $('#cachedFiles').val());

                                fileInput.val('');
                                fileInput.removeAttr('name');

                                var fileUrl = response.files[0].fileUrl;
                                var fileExtension = fileUrl.split('.').pop().toLowerCase();

                                // Expanded MIME types for videos
                                var mimeTypes = {
                                    'mp4'   : 'video/mp4',
                                    'm4v'   : 'video/mp4',
                                    'mov'   : 'video/quicktime',
                                    'avi'   : 'video/x-msvideo',
                                    'mkv'   : 'video/x-matroska',
                                    'webm'  : 'video/webm',
                                    'wmv'   : 'video/x-ms-wmv',
                                    'flv'   : 'video/x-flv',
                                    'ogv'   : 'video/ogg',
                                    'mpeg'  : 'video/mpeg',
                                    'mpg'   : 'video/mpeg',
                                    '3gp'   : 'video/3gpp',
                                    '3g2'   : 'video/3gpp2',
                                    'ts'    : 'video/mp2t',
                                    'vob'   : 'video/mpeg',
                                    'asf'   : 'video/x-ms-asf',
                                    'rm'    : 'application/vnd.rn-realmedia',
                                    'rmvb'  : 'application/vnd.rn-realmedia-vbr',
                                    'divx'  : 'video/divx',
                                    'xvid'  : 'video/xvid',
                                    'f4v'   : 'video/mp4',
                                    'hevc'  : 'video/hevc',
                                    'h264'  : 'video/h264',
                                    'h265'  : 'video/hevc'
                                };

                                // Expanded list of supported image formats
                                var supportedImageExtensions = [
                                    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'ico', 'svg',
                                    'tiff', 'tif', 'jfif', 'heic', 'heif', 'avif'
                                ];

                                var fileContainer = fileInput.closest('.append_type_file');
                                console.log('Appending preview to:', fileContainer);

                                fileContainer.find('.image_structure, .video_structure').remove();

                                var previewHtml = '';
                                if (supportedImageExtensions.includes(fileExtension)) {
                                    previewHtml = `<div class="image_structure"><img src="${fileUrl}" class="image_preview" onerror="console.error('Failed to load image: ${fileUrl}')"></div>`;
                                } else if (Object.keys(mimeTypes).includes(fileExtension)) {
                                    var mimeType = mimeTypes[fileExtension] || 'video/mp4';
                                    previewHtml = `<a href="${fileUrl}" data-fancybox="" class="video_fancy_box"><div class="video_structure"><video controls class="video_preview" style="max-width: 100%;" loop autoplay><source src="${fileUrl}" type="${mimeType}"></video></div></a>`;
                                } else {
                                    console.warn('Unsupported file extension:', fileExtension);
                                    previewHtml = `<div class="unsupported_file">Preview not available for .${fileExtension} files</div>`;
                                }

                                fileContainer.attr('data-url', fileUrl);
                                fileContainer.append(previewHtml);

                                console.log('Preview HTML appended:', previewHtml);

                            } catch (e) {
                                console.error('Error processing fileSuccess:', e);
                                alert('Failed to process file upload response');
                            }

                        });

                        resumable.on('fileError', function (file, response) {
                            ongoingUploads--;
                            if (ongoingUploads === 0) {
                                $('#next-btn').removeAttr('disabled');
                            }
                            $('.pre_loader').hide(); // Hide loader
                            alert('File upload error.');
                        });

                        function updateProgress(file, value) {
                            var progressBar = fileInput.closest('.append_type_wrapper').find('.fileProgress').eq(0);
                            var progressText = fileInput.closest('.append_type_wrapper').find('.progressText').eq(0);
                            progressBar.val(value);
                            progressText.text(value + '%');

                            if (value >= 100) {
                                setTimeout(function() {
                                    progressWrapper.hide(); // Hide instead of remove to maintain layout
                                }, 1000);
                            }
                        }

                    }

                });

                $(document).on('click', '.append_img_div_remove', function () {

                    var fileWrapper = $(this).closest('.append_type_wrapper');
                    var fileContainer = fileWrapper.find('.append_type_file');

                    var fileUrl = fileContainer.data('url');

                    if (fileUrl) {
                        $.ajax({
                            url: '<?php echo e(route('delete.file.aws')); ?>',
                            type: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>',
                                fileUrl: fileUrl
                            },
                            success: function(response) {
                                if (response.success) {
                                    console.log('File deleted from AWS:', fileUrl);

                                    var fileIndex = fileUrls.indexOf(fileUrl);
                                    if (fileIndex !== -1) {
                                        fileUrls.splice(fileIndex, 1);
                                    }
                                    $('#cachedFiles').val(JSON.stringify(fileUrls));

                                } else {
                                    console.error('Failed to delete file from AWS:', fileUrl);
                                    alert('Error deleting file.');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Error deleting file:', error);
                                alert('Error deleting file.');
                            }
                        });
                    }else{
                        ongoingUploads--;
                        if (ongoingUploads === 0) {
                            $('#next-btn').removeAttr('disabled');
                        }

                    }

                    var fileInput = fileWrapper.find('.file-input')[0];
                    if (resumable) {
                        resumable.cancel();
                    }
                        // fileWrapper.find('.progressWrapper').remove();
                        // fileWrapper.find('.append_img_div_remove').hide();
                    $('.pre_loader').hide();
                });

                //COMMENT BY UNAIZ
                // $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function (event) {
                //     const file = $(this)[0].files[0];
                //     const $parentDiv = $(this).closest(".append_type_file");
                //     if (file) {
                //         const imageURL = URL.createObjectURL(file);
                //         $parentDiv.find(".image_structure, .video_structure").remove();
                //
                //         const fileExtension = file.name.split('.').pop().toLowerCase();
                //         if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                //             $parentDiv.append('<div class="image_structure"><img src="' + imageURL + '" class="image_preview" /></div>');
                //         } else if (['mp4', 'mov', 'avi', 'mkv'].includes(fileExtension)) {
                //             $parentDiv.append('<div class="video_structure"><video controls class="video_preview"><source src="' + imageURL + '" type="video/' + fileExtension + '"></video></div>');
                //         }
                //
                //         $parentDiv.find(".append_img_div_remove").show();
                //     } else {
                //         $parentDiv.find(".append_img_div_remove").hide();
                //     }
                // });


                $('#jobForm').on('submit', function () {
                    console.log('Form Data:', $(this).serialize());
                    console.log('Cached Files:', $('#cachedFiles').val());
            });

            });


            $(document).on("input", ".project_budget_min", function () {
                var value = $(this).val();
                // Allow only positive numbers and decimals
                value = value.replace(/[^0-9.]/g, '');
                if ((value.match(/\./g) || []).length > 1) {
                    value = value.substring(0, value.length - 1);
                }//ends if....

                $(this).val(value);
            });

            // // Remove file input on close button click
            // $(document).on("click", ".append_img_div_remove", function() {
            //     $(this).closest(".append_type_wrapper").remove();
            // });


            // Handle click on close button to remove the uploaded file input div
            $(document).on("click", ".append_type_wrapper .append_img_div_remove", function() {
                $(this).closest(".append_type_wrapper").remove();
                // updateRemoveButtonVisibility();
            });

            //COMMENT BY UNAIZ +++ PREVIEW IMAGE
            // Handle file input change to show image preview
            // $(document).on("change", ".append_type_wrapper .append_type_file input[type='file']", function(event) {
            //     const file = $(this)[0].files[0];  // Get the selected file
            //     const $parentDiv = $(this).closest(".append_type_file");
            //     if (file) {
            //         // Handle image file
            //         const imageURL = URL.createObjectURL(file);
            //         // Remove existing image preview before appending a new one
            //         $parentDiv.find(".image_structure").remove();
            //         // Append image preview
            //         $parentDiv.append('<div class="image_structure"><img src='+ imageURL +' class="image_preview" /></div>');
            //         $parentDiv.find(".append_img_div_remove").show();
            //     } else {
            //         $parentDiv.find(".append_img_div_remove").hide();
            //     }
            // });

            function updateRemoveButtonVisibility() {
                $(".append_type_file").each(function () {
                    const hasImage = $(this).find(".image_preview").length > 0;
                    $(this).find(".append_img_div_remove").toggle(hasImage);
                });
            }

            $(".datepicker").datepicker({
                autoclose: true,
                todayHighlight: true,
                todayBtn: "linked",
                startDate: new Date(new Date().setDate(new Date().getDate() + 1))
            }).datepicker('update', new Date(new Date().setDate(new Date().getDate() + 1)));
        });
    </script>

    <script>
        const priceRange = document.getElementById('priceRange');
        const maxPriceRange = document.getElementById('maxPriceRange');
        const minPriceLabel = document.getElementById('minPrice');
        const maxPriceLabel = document.getElementById('maxPrice');

        priceRange.addEventListener('input', updatePriceLabels);
        maxPriceRange.addEventListener('input', updatePriceLabels);

        function updatePriceLabels() {
            minPriceLabel.textContent = priceRange.value;
            maxPriceLabel.textContent = maxPriceRange.value;

            if (parseInt(priceRange.value) >= parseInt(maxPriceRange.value)) {
                priceRange.value = maxPriceRange.value;
            }

            if (parseInt(maxPriceRange.value) <= parseInt(priceRange.value)) {
                maxPriceRange.value = priceRange.value;
            }
        }
    </script>

<script>
    var currentStep = 1; // Set initial step
    $(document).ready(function() {

        // Function to update step based on index
        function updateStep(step) {
            // Hide all step content
            $(".tab").hide();
            $("#tab-" + step).show();  // Show the current step's tab
            $("span.check").hide(); // Hide checkmarks initially
            $("span.step_number").show();

            // Progress bar updates
            for (var i = 1; i <= 3; i++) {
                if (i <= step) {
                    $("span#step_number-" + i).show();
                    $("span#check-" + i).hide();

                    $("#step-" + i + " span").css({
                        "background": "#BE0B31",
                        "color": "#fff",
                        "font-size": "20px",
                        "font-weight": "700",
                        "border": "0",
                    });
                    $("#step-" + i + " h5").css({
                        "color": "black"
                    });
                    $("#custom_steps-" + i).css({
                        "background": "#2280C2",
                    });
                }
                else {
                    $("#custom_steps-" + i).css({
                        "background": "#ddd"
                    });
                    $("#step-" + i + " span").css({
                        "background": "#FFF",
                        "color": "#1B1732",
                        "border": "2px solid #002768",
                    });
                    $("#step-" + i + " h5").css({
                        "color": "#DBDBDB"
                    });
                }
            }

            // Deactivate background for previous steps
            for (var i = 1; i < step; i++) {
                $("span#step_number-" + i).hide();
                $("span#check-" + i).show();
                $("span#check-" + i).css({
                    "background": "#C6F6E6",  // Inactive background for previous steps
                    "color": "#2AB888",
                    "font-size": "22px"
                });
            }
            // Manage button visibility
            if (step === 3) {
                var selectedCategoryId    = parseInt($('#category_id').val(), 10);
                var selectedSubcategoryId = parseInt($('.subcategory:checked').val(), 10);
                var utilType              = $('#vehicle_field_8').val();
                var newGarden             = $('#vehicle_field_85').val();
                var furnaceInstalled      = $('#vehicle_field_108').val();

                // console.log("Selected Category ID:", selectedCategoryId);
                // console.log("Selected Subcategory ID:", selectedSubcategoryId);

                var selectedCategoryElement = $('.show_stepper_form[data-id="' + selectedCategoryId + '"]');
                var visitRequiredData = selectedCategoryElement.data('visit-required');
                // console.log("Visit Required Data:", visitRequiredData);

                var visitRequired = false;

                visitRequiredData.forEach(function(subcategory) {
                    if (subcategory.id === selectedSubcategoryId && subcategory.visit_required === 1) {
                        visitRequired = true;
                    }
                });

                // console.log("Visit Required for this subcategory:", visitRequired);
                // console.log(furnaceInstalled);


                // Show/hide date and time fields based on visit_required
                if (visitRequired) {
                    $('input[name="visit_date"]').closest('.col-md-6').show();
                    $('select[name="visit_time"]').closest('.col-md-6').show();
                    // Show date and time fields and make them required
                    $('input[name="visit_date"]').prop('required', true);
                    $('select[name="visit_time"]').prop('required', true);
                    $('div.post_project_content h5:contains("Date & Time for Staff Site Visit")').show();

                    if (utilType == 'ELECTRIC' || utilType == 'GAS'){
                        $('input[name="visit_date"]').closest('.col-md-6').hide();
                        $('select[name="visit_time"]').closest('.col-md-6').hide();
                    }

                } else {
                    // Hide date and time fields and remove required attribute
                    $('input[name="visit_date"]').closest('.col-md-6').hide();
                    $('select[name="visit_time"]').closest('.col-md-6').hide();

                    $('input[name="visit_date"]').prop('required', false);
                    $('select[name="visit_time"]').prop('required', false);
                    $('div.post_project_content h5:contains("Date & Time for Staff Site Visit")').hide();

                    if (utilType == 'other'){

                        $('input[name="visit_date"]').closest('.col-md-6').show();
                        $('select[name="visit_time"]').closest('.col-md-6').show();
                    }
                    if (newGarden == 'New flower bed/garden'){

                        $('input[name="visit_date"]').closest('.col-md-6').show();
                        $('select[name="visit_time"]').closest('.col-md-6').show();
                    }
                    if (furnaceInstalled == '-No (One furnace)' || furnaceInstalled == 'Unsure' ){

                        $('input[name="visit_date"]').closest('.col-md-6').show();
                        $('select[name="visit_time"]').closest('.col-md-6').show();
                    }
                }
            }


            if (currentStep == 3 ) {
//                $('.modal_btn .btn.btn_black').text('Post');  // Change button text to "Submit" on the last step
                $('.modal_btn button.btn.btn_black').attr('type',"submit");
//                alert("Post Submitted");
//                $(".submit_post").submit();
            } else {
//                $('.modal_btn .btn.btn_black').text('Next');  // Reset button text to "Next"
            }

        };

        // Initially show the first step
        updateStep(currentStep);

        // Next button click event
        $('.show_stepper_form').click(function(){

            id =  $(this).attr('data-id');
            currentStep = 1;
            updateStep(currentStep);
            $('#stepper_form').modal('show');
            $('#category_id').val(id);

             $.ajax({
                url: '<?php echo e(url("get_sub_category")); ?>',
                type: 'GET',
                data:{id:id},
                success: function(response) {
                    $('#show_all_subcategory_div').html(response);
                    id = $('.subcategory:checked').val();
                    get_job_question(id)
                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                }
            });
        });
        $(document).on("change",".question_type_select",function() {
            if($(this).val() == 'other' && $('option:selected', this).attr('data-type') == 'other_input'){
                $(this).closest('.txt_field').find('.question_type_select_div').html('<textarea rows="4" class="form-control other_option" id="" placeholder="Type Here" name='+$(this).attr('name')+'></textarea>')
                $(this).attr('data-original-name', $(this).attr('name')).attr('name', '');
            }else if($(this).val() == 'other' && $('option:selected', this).attr('data-type') == 'image_input'){
                $(this).closest('.txt_field').find('.question_type_select_div').html(' <div class="profile_picture"><input type="file" id="" class="dropify myinput form-control" data-default-file="website/assets/images/website_img.png" name='+$(this).attr('name')+' required/></div>')
                $(this).attr('data-original-name', $(this).attr('name')).attr('name', '');
            }
            else{
                $(this).closest('.txt_field').find('.question_type_select_div').html('');
                $(this).attr('name', $(this).attr('data-original-name') || $(this).attr('name'));
            }
        });
        $(document).on("click",".subcategory",function() {
           id = $('.subcategory:checked').val();
           get_job_question(id)
        });

        $(document).on('change', '.file_input', function () {
            var $currentInput = $(this);
            var file = $currentInput[0].files[0];
            var formData = new FormData();
            var errorMessage = $currentInput.closest('form').find('.file_size_error')
            if(file) {
                const { size } = file;
                if (size > 5 * 1024 * 1024) {  // 5MB limit
                    errorMessage.text('File size exceeds 5MB!');
                    $(this).val('');
                } else {
                    errorMessage.text('');
                }
            }
            formData.append('file', file);
            formData.append('_token', '<?php echo e(csrf_token()); ?>'); // Wrap the token in quotes
            $.ajax({
                url: '<?php echo e(route("upload_jobs_attachment")); ?>',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function (data) {
                    $currentInput.closest('.txt_field').find('.file_name_display').val(data.filename);
                }//ends success....
            });
        });

        function get_job_question(id){
            $.ajax({
                url: '<?php echo e(url("get_sub_category_question")); ?>/' + id,
                type: 'GET',
                // data:{id:id},
                success: function(response) {
                    $('#job_qustion_div').html(response);
                    $(document).ready(function () {
                        $('.custom_multiselect').select2({
                            placeholder: "Select An Option",
                            allowClear: true
                        });
                    })

                },
                error: function(xhr) {
                    console.error(xhr.responseText);
                }
            });
        }

        $('.modal_btn button.btn.btn_black').click(function(e) {
            e.preventDefault();
            var $thisBtn = $(this);
            var valid = true;

            $("#tab-" + currentStep).find(".error-message").remove();
            $("#tab-" + currentStep).find("[required]").each(function() {
                let inputType = $(this).attr("type");
                let value = $(this).val();
                if (currentStep === 3 && inputType  === "file") {
                    return;
                }
                // let isValidInput = value && value.trim() != "";
                let isValidInput = value;

                if (!isValidInput) {
                    valid = false;
                    $(this).addClass("is-invalid");
                    if ($(this).next(".error-message").length === 0) {
                        $(this).after(`<div class="error-message text-danger" style="font-size: 13px;">This field is required.</div>`);
                    }
                } else {
                    $(this).removeClass("is-invalid");
                    $(this).next(".error-message").remove();
                }
            });
            if (valid) {
                if (currentStep == 3) {
                    $thisBtn.prop("disabled", true).text("Submitting...");
                    $(".submit_post").submit();
                } else {
                    currentStep++;
                    updateStep(currentStep);
                }
            } else {
                alert("Please fill in all required fields.");
            }
        });

        $(document).on('click','.modal_btn a.btn_black', function () {
            currentStep = 1;
            $("span.step_number").show();
            $("span.check").hide();
            updateStep(currentStep);
        });

        // Previous button click event
        $('.modal_btn .btn.btn_transparent').click(function() {
            if (currentStep > 1) {
                currentStep--;
                updateStep(currentStep);
            }
        });

        // Radio button checked value to show form
        $('.custom_form_radio .custom_radio input[type=radio]').change(function() {
            var selectedValue = $('input[name="formRadio"]:checked').val();

            if (selectedValue === "chooseJob") {
                $(".choose_job_hidden_field").show();
                $(".fill_requirement_hidden_field, .post_project_hidden_field").hide();
            } else if (selectedValue === "fillRequirement") {
                $(".fill_requirement_hidden_field").show();
                $(".choose_job_hidden_field, .post_project_hidden_field").hide();
            } else if (selectedValue === "postProject") {
                $(".post_project_hidden_field").show();
                $(".choose_job_hidden_field, .fill_requirement_hidden_field").hide();
            }
        });

        // Select option handling
        $("textarea.other_option").hide();
        $(".txt_field .profile_picture").hide();
        $(".txt_field label:has(a) a").hide();
        $(".txt_field select").change(function() {
            var selectOption = $(this).find('option:selected').val();

            if (selectOption === "other") {
//                $(this).hide();
                $(this).closest(".txt_field").find('textarea.other_option').show();
                $(this).closest(".txt_field").find(".profile_picture").show();
                $(this).closest(".txt_field").find('label:has(a) a').show();
            }
        });

        // Switching back to select dropdown when 'a' tag is clicked
        $(".txt_field label:has(a) a").click(function() {
            $(this).hide();
            $(this).closest(".txt_field").find("textarea.other_option").hide();
            $(this).closest(".txt_field").find(".profile_picture").hide();
            $(this).closest(".txt_field").find("select").show();
        });

        $('.dropify').dropify();
        $(".custom_modal .txt_field .profile_picture .dropify-wrapper button.dropify-clear").html("");

//        Add Address Jquery
        $(".another_location").hide();
        $(document).on("click",".add_another_address button",function () {
            $(".another_location").show();
            $(".another_location .check_address input[type=checkbox]").prop({ checked: true, required: true });
            $(".another_location .check_address input[type=text]").prop('required', true);
            $(".custom_address .check_address input[type=checkbox]").prop({ checked: false, required: false });
            $(".add_another_address").hide();
        });

        $(document).on("click",".remove_address button",function () {
            $(".another_location").hide();
            $(".another_location .check_address input[type=checkbox]").prop({ checked: false, required: false });
            $(".custom_address .check_address input[type=checkbox]").prop({ checked: true, required: true });
            $(".add_another_address").show();
            $(".add_another_address button").prop("disabled", true);
            $(".another_location .check_address input[type=text]").prop('required', false);
        });

        $(document).on("change", ".custom_address .check_address input[type=checkbox]", function () {
            if ($(this).is(":checked")) {
                $(".another_location .check_address input[type=checkbox]").prop({ checked: false, required: false });
                $(".another_location .check_address input[type=text]").prop('required', false);
                $(".custom_address .check_address input[type=checkbox]").prop({ checked: true, required: true });
            }
        });

        $(document).on("change", ".another_location .check_address input[type=checkbox]", function () {
            if ($(this).is(":checked")) {
                $(".custom_address .check_address input[type=checkbox]").prop({ checked: false, required: false });
                $(".another_location .check_address input[type=text]").prop('required', true);
                $(".another_location .check_address input[type=checkbox]").prop({ checked: true, required: true });
            }
        });

        $(".add_another_address button").prop("disabled", true);  // Disable the button
        $(document).on("change", ".custom_address .check_address input[type=checkbox]", function () {
            var selectedCheckbox = $('.custom_address .check_address input[type=checkbox]:checked');  // Get all checked checkboxes
            if (selectedCheckbox.length > 0) {
                $(".add_another_address button").prop("disabled", true);  // Disable the button
            } else {
                $(".add_another_address button").prop("disabled", false);  // Enable the button
            }
        });

    });

        //parent searching category
        // $(document).ready(function () {
        //     $("#categorySearch").on("keyup", function () {
        //         let searchValue = $(this).val().toLowerCase(); // Get the input value and convert to lowercase
        //         $("#categoryList .custom_column").filter(function () {
        //             let categoryName = $(this).data("name").toLowerCase(); // Get the category name from the data attribute
        //             if (categoryName.includes(searchValue)) {
        //                 $(this).show(); // Show the matching categories
        //             } else {
        //                 $(this).hide(); // Hide non-matching categories
        //             }
        //         });
        //     });
        //     $("#searchBtn").on("click", function () {
        //         $("#categorySearch").trigger("keyup"); // Trigger the same functionality as typing
        //     });
        // });

        //parent/child  searching category
    $(document).ready(function () {
        $("#categorySearch").on("keyup", function () {
                let searchValue = $(this).val().toLowerCase().trim(); // Get input value and convert to lowercase
                $("#categoryList .custom_column").each(function () {
                    let categoryName = $(this).data("name").toLowerCase(); // Parent category name
                    let subCategories = $(this).data("child-category") ? $(this).data("child-category").toLowerCase().split(',') : []; // Sub-categories array

                    let matchFound = subCategories.some(sub => sub.includes(searchValue)); // Check if any sub-category matches

                    if (categoryName.includes(searchValue) || matchFound) {
                        $(this).show(); // Show if match found
                } else {
                        $(this).hide(); // Hide if no match
                }
            });
        });
        $("#searchBtn").on("click", function () {
                $("#categorySearch").trigger("keyup"); // Trigger search on button click
        });
    });
    // $(document).ready(function () {
    //     $('#stepper_form').on('shown.bs.modal', function () {
    //         $('.pac-container').css('z-index', '1111');
    //     });
    //     $('#stepper_form').on('hidden.bs.modal', function () {
    //         $('.pac-container').css('z-index', '');
    //     });
    // });

    // google.maps.event.addDomListener(window, 'load', function () {
    //     var autocomplete = new google.maps.places.Autocomplete(
    //         document.getElementById('custom_address')
    //     );
    //     google.maps.event.addListener(autocomplete, 'place_changed', function () {
    //         var place = autocomplete.getPlace();
    //         var addressComponents = place.address_components;
    //         let city = '';
    //         let state = '';
    //         let postalCode = '';
    //         let country = '';
    //         let latitude = '';
    //         let longitude = '';
    //
    //         // Extract address components
    //         addressComponents.forEach((component) => {
    //             const types = component.types;
    //             if (types.includes('locality')) {
    //                 city = component.long_name;
    //             }
    //             if (types.includes('administrative_area_level_1')) {
    //                 state = component.long_name;
    //             }
    //             if (types.includes('postal_code')) {
    //                 postalCode = component.long_name;
    //             }
    //             if (types.includes('country')) {
    //                 country = component.long_name;
    //             }
    //         });
    //
    //         // Extract latitude and longitude
    //         if (place.geometry && place.geometry.location) {
    //             latitude = place.geometry.location.lat();
    //             longitude = place.geometry.location.lng();
    //         }
    //
    //         // Assign values to hidden inputs
    //         document.getElementById('custom_city').value = city || '';
    //         document.getElementById('custom_state').value = state || '';
    //         document.getElementById('custom_postal').value = postalCode || '';
    //         document.getElementById('custom_country').value = country || '';
    //         document.getElementById('custom_lat').value = latitude || '';
    //         document.getElementById('custom_lng').value = longitude || '';
    //     });
    // });


    function initAutocomplete() {
        var autocomplete = new google.maps.places.Autocomplete(
            document.getElementById('custom_address')
        );
        google.maps.event.addListener(autocomplete, 'place_changed', function () {
            var place = autocomplete.getPlace();
            var addressComponents = place.address_components;
            let city = '';
            let state = '';
            let postalCode = '';
            let country = '';
            let latitude = '';
            let longitude = '';

            // Extract address components
            addressComponents.forEach((component) => {
                const types = component.types;
                if (types.includes('locality')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.long_name;
                }
                if (types.includes('postal_code')) {
                    postalCode = component.long_name;
                }
                if (types.includes('country')) {
                    country = component.long_name;
                }
            });

            // Extract latitude and longitude
            if (place.geometry && place.geometry.location) {
                latitude = place.geometry.location.lat();
                longitude = place.geometry.location.lng();
            }

            // Assign values to hidden inputs
            document.getElementById('custom_city').value = city || '';
            document.getElementById('custom_state').value = state || '';
            document.getElementById('custom_postal').value = postalCode || '';
            document.getElementById('custom_country').value = country || '';
            document.getElementById('custom_lat').value = latitude || '';
            document.getElementById('custom_lng').value = longitude || '';
        });
    }
</script>
<script src="<?php echo e(asset('plugins/components/dropify/dist/js/dropify.min.js')); ?>"></script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mrdoall\resources\views/website/buyer/buyer_home.blade.php ENDPATH**/ ?>