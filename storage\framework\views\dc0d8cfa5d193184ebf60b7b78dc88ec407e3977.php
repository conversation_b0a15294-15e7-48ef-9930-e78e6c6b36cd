<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="description" content="<?php echo e(App\CommonSetting::first()->description??''); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('')); ?><?php echo e(App\CommonSetting::first()->favicon??''); ?>">
    <title><?php echo e(App\CommonSetting::first()->title??''); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"  rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"/>
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('plugins/components/dropzone-master/dist/dropzone.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('plugins/components/datatables/jquery.dataTables.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
    <?php echo $__env->yieldPushContent('css'); ?>
</head>
<body>

<?php if(Auth::check() && auth()->user()->hasRole('buyer')): ?>
    
    <header class="master_navbar">
        <nav class="navbar navbar-expand-lg ">
            <div class="container-fluid">
                <div class="site_logo">
                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png" alt="Mr.Do All"></a>
                </div>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav">
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('home')); ?>">Home</a>
                        </li>
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('buyer_home')); ?>">Explore</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(url('contact_us')); ?>">Contact Us</a>
                        </li>
                    </ul>
                </div>
                <div class="header_btn">
                    <a class="buyer_top_icons" href="<?php echo e(url('buyer_chat')); ?>"><i class="fa-solid fa-message"></i></a>
                    <div class="dropdown website_notification">
                        <a href="<?php echo e(url('website_notification')); ?>" class="buyer_top_icons dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="
                            fa-solid fa-bell "></i>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <div class="notification_top">
                                <div><h4>Notifications</h4></div>
                            </div>
                            <li>
                                <div class="message-center">
                                    <?php if($notifications->isNotEmpty()): ?>
                                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                            <?php if($notification->type == 'job_posted'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>



                                                <?php if($notification->type == 'job_staff_assigned'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'milestoneDocsUploaded'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'job_payment_accepted'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'jobOfferBidBuyer'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'job_reposted'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6>Job Re-posted</h6>
                                                                    <p><?php echo e($notification->data['message']??''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'jobOfferBidBuyer'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">






















                                                        </div>
                                                    </a>
                                                <?php endif; ?>


                                            <?php if($notification->type == 'DocApproved'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            <div>
                                                                <h6><?php echo e($notification->data['subject'] ??''); ?></h6>
                                                                <p><?php echo e(ucwords($notification->data['message']??'')); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_assigned'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">



                                                            <div>
                                                                <h6>Job Assign</h6>
                                                                <p>Hello <?php echo e(ucwords(auth()->user()->name??'')); ?>, your job has been successfully assigned to <?php echo e(ucwords($notification->data['name'] ?? 'a seller')); ?> (<?php echo e($notification->data['job_title'] ?? ''); ?>).</p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_status_staff'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p>
                                                                    <?php echo e($notification->job_status == 'job_rejected_staff' ? 'Heads up!' : 'Good news!'); ?>

                                                                    <?php echo e($notification->data['name'] ?? ''); ?> has
                                                                    <?php echo e($notification->job_status == 'job_rejected_staff' ? 'rejected' : 'agreed to take on'); ?>

                                                                    your project: "<?php echo e($notification->data['job_title'] ?? ''); ?>"
                                                                </p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <p>No notifications</p>
                                    <?php endif; ?>
                                </div>
                            </li>
                            
                            <div class="view_all">
                                <a href="<?php echo e(url('website_notification')); ?>"><h6>View All</h6></a>
                            </div>
                            
                        </ul>
                    </div>
                    <div class="dropdown website_dropdown">
                        <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="profile_img">
                                <img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" class="img-circle"
                                     alt="user-img" />
                            </div>
                            <div class="buyer_account">
                                
                                <?php echo e(Auth::user()->first_name??''); ?>

                                <?php echo e(Auth::user()->last_name??''); ?>

                            </div>
                        </button>
                        <ul class="dropdown-menu service_provider_dropdown">
                            <li>
                                <a class="seller_dropdown" href="<?php echo e(url('buyer_home')); ?>">
                                    <div class="custom_dropdown">
                                        <div class="profile_inside_img"><img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" alt=""></div>
                                        <div class="profile_title">
                                            <h4><?php echo e(Auth::user()->name); ?></h4>
                                            <h6><?php echo e(Auth::user()->email); ?></h6>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('my_profile')); ?>"><h5>My Profile</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('my_projects')); ?>"><h5>My Projects</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('buyer_chat')); ?>"><h5>Chat </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                            
                            <li><a class="dropdown-item dropdown_option signout" href="<?php echo e(url('logout')); ?>"><h5>Sign Out </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                        </ul>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>
<?php elseif(Auth::check() && auth()->user()->hasRole('seller')): ?>
    <header class="master_navbar">
        <nav class="navbar navbar-expand-lg ">
            <div class="container-fluid">
                <div class="site_logo">
                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png" alt="Mr.Do All"></a>
                </div>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav">
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('home')); ?>">Home</a>
                        </li>
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('seller_home')); ?>">Explore</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"  href="<?php echo e(url('contact_us')); ?>">Contact Us</a>
                        </li>
                    </ul>
                </div>
                <div class="header_btn">
                    

                    <a class="buyer_top_icons" href="<?php echo e(url('service_provider_chat')); ?>"><i class="fa-solid fa-message"></i></a>
                    <div class="dropdown website_notification">
                        <a href="<?php echo e(url('website_notification')); ?>" class="buyer_top_icons dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-bell "></i>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">

                            <div class="notification_top">
                                <div><h4>Notifications</h4></div>
                            </div>
                            <li>
                                <div class="message-center">
                                    <?php if($notifications->isNotEmpty()): ?>



                                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($notification->type == 'DocRejected'): ?>
                                                <?php if(empty($notification->read_at)): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                
                                                                
                                                                
                                                                <div>
                                                                    <h6><?php echo e($notification->data['subject'] ??''); ?></h6>
                                                                    <p><?php echo e($notification->data['message']??''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <span><i class="fa-solid fa-circle"></i></span>
                                                                <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif; ?>


                                                <?php if($notification->type == 'job_staff_assigned'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'jobOfferBidSeller'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if($notification->type == 'job_payment_accepted'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>
                                            <?php if($notification->type == 'DocApproved'): ?>
                                                <?php if(empty($notification->read_at)): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                
                                                                
                                                                
                                                                <div>
                                                                    <h6><?php echo e($notification->data['subject'] ??''); ?></h6>
                                                                    <p><?php echo e($notification->data['message']??''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <span><i class="fa-solid fa-circle"></i></span>
                                                                <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_offer_awarded'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>Job Offer Approve</h6>
                                                                <p>Congratulations! <?php echo e($notification->data['name'] ?? ''); ?> has accepted your bid and awarded you the job "<?php echo e($notification->data['title'] ?? ''); ?>" for <?php echo e($notification->data['amount'] ?? ''); ?>.</p>
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_assigned'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>Job Assign</h6>
                                                                The admin has assigned you the job "<?php echo e($notification->data['title'] ?? ''); ?>" for <?php echo e($notification->data['max_amount'] ?? ''); ?>.
                                                            </div>
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>


                                </div>
                            </li>
                            
                            <div class="view_all">
                                <a href="<?php echo e(url('website_notification')); ?>"><h6>View All</h6></a>
                            </div>
                            
                        </ul>
                    </div>
                    <div class="dropdown website_dropdown">
                        <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="profile_img">
                                <img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" class="img-circle"
                                     alt="user-img" />
                            </div>
                            <div class="buyer_account">
                                
                                <?php echo e(Auth::user()->name); ?>

                            </div>
                        </button>
                        <ul class="dropdown-menu service_provider_dropdown">
                            <li>
                                <a class="seller_dropdown" href="<?php echo e(url('seller_home')); ?>">
                                    <div class="custom_dropdown">
                                        <div class="profile_inside_img"><img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" alt=""></div>
                                        <div class="profile_title">
                                            <h4><?php echo e(Auth::user()->name); ?></h4>
                                            <h6><?php echo e(Auth::user()->email); ?></h6>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_profile ')); ?>"><h5>My Profile</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('all_projects')); ?>"><h5>My Projects</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('seller_bids')); ?>"><h5>My Bids</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('ewallet')); ?>"><h5>Wallet</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_provider_chat')); ?>"><h5>Chat </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                            
                            <li><a class="dropdown-item dropdown_option signout" href="<?php echo e(url('logout')); ?>"><h5>Sign Out </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                        </ul>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>
<?php elseif(Auth::check() && auth()->user()->hasRole('user')): ?>
    <header class="master_navbar">
        <nav class="navbar navbar-expand-lg ">
            <div class="container-fluid">
                <div class="site_logo">
                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png" alt="Mr.Do All"></a>
                </div>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav">
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('home')); ?>">Home</a>
                        </li>
                    </ul>
                </div>
                <div class="header_btn">
                    
                    
                    <div class="dropdown website_notification">
                        <a href="<?php echo e(url('website_notification')); ?>" class="buyer_top_icons dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-bell "></i>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <div class="notification_top">
                                <div><h4>Notifications</h4></div>
                            </div>
                            <li>
                                <div class="message-center">
                                    <?php if($notifications->isNotEmpty()): ?>
                                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($notification->type == 'job_posted'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>New Job Posted</h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has created a new job listing: "<?php echo e($notification->data['project_title'] ?? ''); ?>"</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'job_status_staff'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6><?php echo e($notification->data['title'??'']); ?></h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has <strong><?php echo e($notification->job_status == 'job_rejected_staff' ? 'rejected' : 'taken'); ?></strong> the project "<?php echo e($notification->data['job_title'] ?? ''); ?>"
                                                                    submitted by <?php echo e($notification->data['buyer_name'] ?? ''); ?>.</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>


                                                <?php if($notification->type == 'job_payment_accepted'): ?>
                                                    <a href="#!">
                                                        <div class="navbar_notification ">
                                                            <div class="user_profile">
                                                                <div>
                                                                    <h6><?php echo e($notification->data['title'] ?? ''); ?></h6>
                                                                    <p><?php echo e($notification->data['message'] ?? ''); ?></p>
                                                                </div>
                                                            </div>
                                                            <div class="status_time">
                                                                <p><?php echo e(\Carbon\Carbon::parse($notification->created_at)->diffForHumans()); ?></p>
                                                            </div>
                                                        </div>
                                                    </a>
                                                <?php endif; ?>



                                            <?php if($notification->type == 'job_offer_stripe_payment'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>Payment Recived</h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has paid <?php echo e($notification->data['amount']??''); ?> for this job "<?php echo e($notification->data['title'] ?? ''); ?>"</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'disputeCreated'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>Dispute</h6>
                                                                <p><?php echo e($notification->data['sender_name'] ?? ''); ?> has raised a dispute for the job: "<?php echo e($notification->data['job_title'] ?? ''); ?>"</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>

                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'UserRequest'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6>User Category Request</h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has submitted a request to be approved for an additional category.</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($notification->type == 'withdrawal_request'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                            
                                                            
                                                            
                                                            <div>
                                                                <h6><?php echo e($notification->data['title']??''); ?></h6>
                                                                <p><?php echo e($notification->data['name'] ?? ''); ?> has requested a payment of <?php echo e($notification->data['amount'] ?? ''); ?>.</p>
                                                            </div>
                                                            
                                                            
                                                            
                                                            
                                                            
                                                            
                                                        </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                            </li>
                            
                            <div class="view_all">
                                <a href="<?php echo e(url('website_notification')); ?>"><h6>View All</h6></a>
                            </div>
                            


                        </ul>
                    </div>
                    <div class="dropdown website_dropdown">
                        <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="profile_img">
                                <img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" class="img-circle"
                                     alt="user-img" />
                            </div>
                            <div class="buyer_account">
                                
                                <?php echo e(Auth::user()->name); ?>

                            </div>
                        </button>
                        <ul class="dropdown-menu service_provider_dropdown">
                            <li>
                                <a class="seller_dropdown" href="<?php echo e(url('seller_home')); ?>">
                                    <div class="custom_dropdown">
                                        <div class="profile_inside_img"><img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" alt=""></div>
                                        <div class="profile_title">
                                            <h4><?php echo e(Auth::user()->name); ?></h4>
                                            <h6><?php echo e(Auth::user()->email); ?></h6>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_profile ')); ?>"><h5>My Profile</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('all_projects')); ?>"><h5>My Projects</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('seller_bids')); ?>"><h5>My Bids</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('ewallet')); ?>"><h5>Wallet</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_provider_chat')); ?>"><h5>Chat </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                            
                            <li><a class="dropdown-item dropdown_option signout" href="<?php echo e(url('logout')); ?>"><h5>Sign Out </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                        </ul>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>
<?php elseif(Auth::check() && auth()->user()->hasRole('staff')): ?>
    <header class="master_navbar">
        <nav class="navbar navbar-expand-lg ">
            <div class="container-fluid">
                <div class="site_logo">
                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png" alt="Mr.Do All"></a>
                </div>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav">
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php echo e(url('home')); ?>">Home</a>
                        </li>
                    </ul>
                </div>
                <div class="header_btn">
                    
                    
                    <div class="dropdown website_notification">
                        <a href="<?php echo e(url('website_notification')); ?>" class="buyer_top_icons dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-bell "></i>
                        </a>
                        <ul class="dropdown-menu mailbox animated bounceInDown">
                            <div class="notification_top">
                                <div><h4>Notifications</h4></div>
                            </div>
                            <li>
                                <div class="message-center">
                                    <?php if($notifications->isNotEmpty()): ?>
                                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($notification->type == 'job_assigned'): ?>
                                                <a href="#!">
                                                    <div class="navbar_notification ">
                                                        <div class="user_profile">
                                                                <div class="profile_img">
                                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/user_img.png">
                                                                </div>
                                                            <div>
                                                                <h6>New Job Posted</h6>
                                                                <p> You’ve been selected by the admin for the job "<?php echo e($notification->data['job_title'] ?? ''); ?>". Best of luck as you begin the task!</p>
                                                            </div>






                                                              </div>
                                                        <div class="status_time">
                                                            <span><i class="fa-solid fa-circle"></i></span>
                                                            <h5><?php echo e($notification->created_at->diffForHumans()); ?></h5>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>


                                </div>
                            </li>
                            
                            <div class="view_all">
                                <a href="<?php echo e(url('website_notification')); ?>"><h6>View All</h6></a>
                            </div>
                            
                        </ul>
                    </div>
                    <div class="dropdown website_dropdown">
                        <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="profile_img">
                                <img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" class="img-circle"
                                     alt="user-img" />
                            </div>
                            <div class="buyer_account">
                                
                                <?php echo e(Auth::user()->name); ?>

                            </div>
                        </button>
                        <ul class="dropdown-menu service_provider_dropdown">
                            <li>
                                <a class="seller_dropdown" href="<?php echo e(url('seller_home')); ?>">
                                    <div class="custom_dropdown">
                                        <div class="profile_inside_img"><img src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" alt=""></div>
                                        <div class="profile_title">
                                            <h4><?php echo e(Auth::user()->name); ?></h4>
                                            <h6><?php echo e(Auth::user()->email); ?></h6>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_profile ')); ?>"><h5>My Profile</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('all_projects')); ?>"><h5>My Projects</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('seller_bids')); ?>"><h5>My Bids</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('ewallet')); ?>"><h5>Wallet</h5> <i class="fa-solid fa-arrow-right"></i></a></li>
                            <li><a class="dropdown-item dropdown_option" href="<?php echo e(url('service_provider_chat')); ?>"><h5>Chat </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                            
                            <li><a class="dropdown-item dropdown_option signout" href="<?php echo e(url('logout')); ?>"><h5>Sign Out </h5><i class="fa-solid fa-arrow-right"></i></a></li>
                        </ul>
                    </div>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>
<?php else: ?>
    <header class="master_navbar">
        <nav class="navbar navbar-expand-lg ">
            <div class="container-fluid">
                <div class="site_logo">
                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/site_logo.png" alt="Mr.Do All"></a>
                </div>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    
                    
                    
                    <?php if(!Auth::user()): ?>
                        <ul class="navbar-nav">
                            <li class="nav-item active">
                                <a class="nav-link" href="<?php echo e(url('about_us')); ?>">About Us</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(url('contact')); ?>">Contact Us</a>
                            </li>
                            
                            
                            
                            
                            
                            
                            
                            
                        </ul>
                        <div class="header_btn login_btn">
                            <a class="sign_in" href="<?php echo e(url('login')); ?>">Sign In</a>
                            <a href="<?php echo e(url('registration_role')); ?>" class="btn btn_white">Get Started
                                <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="header_btn responsive_dashboard_head">
                            <?php if(Auth::check() && auth()->user()->hasRole('seller')): ?>
                                <a class="sign_in" href="<?php echo e(url('seller_home')); ?>"> Dashboard </a>
                                <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                    <div class="next_btn">
                                        <i class="fa-solid fa-arrow-right">
                                        </i>
                                    </div>
                                </a>
                            <?php elseif(Auth::check() && auth()->user()->hasRole('buyer')): ?>
                                <a class="sign_in" href="<?php echo e(url('buyer_home')); ?>"> Dashboard </a>
                                <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                    <div class="next_btn">
                                        <i class="fa-solid fa-arrow-right">
                                        </i>
                                    </div>
                                </a>
                            <?php else: ?>
                                <a class="sign_in" href="<?php echo e(url('dashboard')); ?>"> Dashboard </a>
                                <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                    <div class="next_btn">
                                        <i class="fa-solid fa-arrow-right">
                                        </i>
                                    </div>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if(Auth::user()): ?>
                    <div class="header_btn dashboard_header">
                        <?php if(Auth::check() && auth()->user()->hasRole('seller')): ?>
                            <a class="sign_in" href="<?php echo e(url('seller_home')); ?>"> Dashboard </a>
                            <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                <div class="next_btn">
                                    <i class="fa-solid fa-arrow-right">
                                    </i>
                                </div>
                            </a >
                        <?php elseif(Auth::check() && auth()->user()->hasRole('buyer')): ?>
                            <a class="sign_in" href="<?php echo e(url('buyer_home')); ?>"> Dashboard </a>
                            <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                <div class="next_btn">
                                    <i class="fa-solid fa-arrow-right">
                                    </i>
                                </div>
                            </a>
                        <?php else: ?>
                            <a class="sign_in" href="<?php echo e(url('dashboard')); ?>"> Dashboard </a>
                            <a href="<?php echo e(url('logout')); ?>" class="btn btn_white">Logout
                                <div class="next_btn">
                                    <i class="fa-solid fa-arrow-right">
                                    </i>
                                </div>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="header_btn website_login_btn">
                        <a class="sign_in" href="<?php echo e(url('login')); ?>">Sign In</a>
                        <a href="<?php echo e(url('registration_role')); ?>" class="btn btn_white">Get Started
                            <div class="next_btn"><i class="fa-solid fa-arrow-right"></i></div>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </nav>
    </header>
<?php endif; ?>
<?php echo $__env->yieldContent('content'); ?>
<footer class="footer_section">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="footer_content">
                    <div class="row ">
                        <div class="col-md-4 col-sm-4 custom_column_align">
                            <div class="footer_detail">
                                <div class="site_logo">
                                    <a class="navbar-brand" href="<?php echo e(url('home')); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/footer_logo.png" alt="Mr.Do All"></a>
                                </div>
                                <h5><?php echo e($footerData->section_two_description??''); ?></h5>
                                <div class="social_icons">
                                    <a href="<?php echo e($footerData->section_two_fb??''); ?>"><i class="fa-brands fa-facebook"></i></a>
                                    <a href="<?php echo e($footerData->section_two_instagram??''); ?>"><i class="fa-brands fa-instagram"></i></a>
                                    <a href="<?php echo e($footerData->section_two_twitter??''); ?>"><i class="fa-brands fa-twitter"></i></a>
                                    <a class="follow" href="<?php echo e(url('login')); ?>">Follow</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4 custom_column_align">
                            <div class="footer_quick_link">
                                <h5>Company</h5>
                                <ul class="navbar-nav">
                                    <li class="nav-item active">
                                        <a class="nav-link active" aria-current="page" href="<?php echo e(url('about_us')); ?>">About</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="<?php echo e(url('contact')); ?>">Contact Us</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4 custom_column_align">
                            <div class="terms_conditions">
                                <h5>Help</h5>
                                <ul class="navbar-nav">
                                    <li class="">
                                        <a href="<?php echo e(url('contact')); ?>">Customer Support</a>
                                    </li>
                                    <li class="">
                                        <a href="<?php echo e(url('terms_condition')); ?>">Terms & Conditions</a>
                                    </li>
                                    <li class="">
                                        <a href="<?php echo e(url('privacy_policy')); ?>">Privacy Policy</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="copyright_section">
                    <p class="copyright ">© Copyright 2024, Mr. Do All, All Right Reserved.</p>
                </div>
            </div>
        </div>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-element-bundle.min.js"></script>
<script src="<?php echo e(asset('plugins/components/dropzone-master/dist/dropzone.js')); ?>"></script>












<script>

    if (typeof window.initAutocomplete !== 'function') {
        window.initAutocomplete = function () {
        };
    }
</script>

<script
        src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('googleAPI')); ?>&libraries=places&callback=initAutocomplete&loading=async"
        defer>
</script>
<script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.21.0/jquery.validate.min.js"></script>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">

    <?php if(session()->has('message')): ?>
    Swal.fire({
        title: "<?php echo e(session()->get('title')??'success!'); ?>",
        html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
        icon: "<?php echo e(session()->get('type')??'success'); ?>",
        timer: 5000,
        buttons: false,
    });
    <?php endif; ?>
    <?php if(session()->has('error_message')): ?>
    Swal.fire({
        title: "<?php echo e(session()->get('title')??'error!'); ?>",
        html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('error_message')))); ?>",
        icon: "<?php echo e(session()->get('type')??'error'); ?>",
        timer: 5000,
        buttons: false,
    });
    <?php endif; ?>
    <?php if(session()->has('flash_message')): ?>
    Swal.fire({
        title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
        
        icon: "<?php echo e(session()->get('type')??'success'); ?>",
        timer: 5000,
        buttons: false,
    });
    <?php endif; ?>
    // function showDeleteConfirmation(button) { //comment by talha
    //     Swal.fire({
    //         title: 'Confirm Delete',
    //         text: 'Are you sure you want to delete this item?',
    //         icon: 'warning',
    //         showCancelButton: true,
    //         confirmButtonColor: '#3085d6',
    //         cancelButtonColor: '#d33',
    //         confirmButtonText: 'Delete',
    //         cancelButtonText: 'Cancel'
    //     }).then((result) => {
    //         if (result.isConfirmed) {
    //             button.closest('.delete-form').submit();
    //         }
    //     });
    // }//end function


    $(document).ready(function (){
        $.validator.addMethod('noRestrictedContent', function(value, element) {
            var restrictedPatterns = [
                /<\?php.*\?>/,                // PHP code injection
                /<script.*>.*<\/script>/,     // JavaScript injection
                /eval\(/,                     // eval() function
                /base64_encode\(/,            // Base64 encoded code
                /system\($_GET\[.*\]\)/,      // Command injection
                /<iframe.*>.*<\/iframe>/,     // iframe injection
                /onerror=.*>/i,               // onerror event handler
                /alert\(/,                    // JavaScript alert()
                /<meta.*http-equiv.*refresh.*content=.*>/i, // Meta refresh injection
                /document\.location/,        // Document location change
                /window\.location/,          // Window location change
                /<.*on\w+=.*>/i,              // Inline event handlers (onerror, onclick, etc.)
            ];

            // Check the value for any restricted pattern
            for (var i = 0; i < restrictedPatterns.length; i++) {
                if (restrictedPatterns[i].test(value)) {
                    return false; // If any pattern matches, return false
                }
            }
            return true; // No restricted content found
        }, "The field contains restricted content (e.g., PHP code, script tags, or other malicious payloads).");

    })
    $('.modal').on('shown.bs.modal', function () {
        $('.pac-container').css('z-index', '1111');
    });

    $('.modal').on('hidden.bs.modal', function () {
        $('.pac-container').css('z-index', '1000');
    });
    $(document).on("input", ".geography_address", function () {
        $('.pac-container').css('z-index', '1111');
    })

</script>

<?php echo $__env->yieldPushContent('js'); ?>

<script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<script>

    
    
    
    
    

    

    
    
    
    
    
    
    
    
    
    
    $(document).ready(function () {
        const pusher = new Pusher('<?php echo e(env('PUSHER_APP_KEY')); ?>', {
            cluster: '<?php echo e(env('PUSHER_APP_CLUSTER')); ?>',
            forceTLS: true
        });

        const userId = <?php echo json_encode(auth()->user()?->id, 15, 512) ?>;

        if (userId) {
            const channel = pusher.subscribe('user.' + userId);

            channel.bind('real-time-Notification', function (data) {
                console.log('🔔 RTNotification received:', data);

                const title = data.data.title || 'Notification';
                const message = data.data.message || 'You have a new notification.';
                const createdAt = data.data.created_at || 'Just now';

                    const newNotif = `
                        <a href="#!">
                            <div class="navbar_notification">
                                <div class="user_profile">
                                    <div>
                                        <h6>${title}</h6>
                                        <p>${message}</p>
                                    </div>
                                </div>
                                <div class="status_time">
                                    <span><i class="fa-solid fa-circle"></i></span>
                                    <h5>${createdAt}</h5>
                                </div>
                            </div>
                        </a>
                    `;

                    $('.message-center').prepend(newNotif);
                    const notifs = $('.message-center a');
                    if (notifs.length > 5) {
                        notifs.slice(5).remove();
                    }
            });
        }
    });


    $(document).ready(function() {
        $(".txt_field .fa-eye").hide();
        $(".txt_field .fa-eye-slash").show();
        $(".custom_eye_icon").click(function(){
            $(this).closest(".txt_field").find(".custom_eye_icon").toggleClass("fa-eye fa-eye-slash")
            var input = $(this).siblings(".password_eye");
            input.attr('type') === 'password' ? input.attr('type','text') : input.attr('type','password')
        });

    });

    $(document).ready(function (){
        $('body').on('click', function (event) {
            if ($('.navbar .navbar-collapse').hasClass('show')) {
                if (!$(event.target).closest('.navbar .navbar-collapse').length && !$(event.target).closest('.navbar-toggler').length) {
                    $('.navbar .navbar-collapse').removeClass('show');
                }
            }
        });
        $('.nav-item').on('click', function (event) {
            event.stopPropagation();
        });
    })

    // Website Animation
    document.addEventListener('DOMContentLoaded', () => {
        const animatedElements = document.querySelectorAll('.custom_card, h1, .btn');

        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';  // Play animation when in viewport
                    observer.unobserve(entry.target);  // Stop observing once animation has played
                }
            });
        }, {
            threshold: 0.1,  // Trigger animation when 10% of the element is visible
        });

        // Observe each animated element
        animatedElements.forEach(el => {
            el.style.animationPlayState = 'paused';  // Pause animation initially
            observer.observe(el);
        });
    });

    $(document).ready(function () {
        $("#subscribeForm").submit(function (e) {
            e.preventDefault();
            let email = $("#email").val();
            let _token = $("input[name=_token]").val();
            $.ajax({
                url: "<?php echo e(route('subscribe')); ?>",
                type: "POST",
                data: {
                    email: email,
                    _token: _token
                },
                success: function (response, status, xhr) {
                    // console.log("Error code: " + xhr.status);
                    $(".is_subscribe").html('<p style="color: green;">' + response.message + '</p>');
                    $("#email").val("");
                    setTimeout(function() {
                        $(".is_subscribe").fadeOut();
                    }, 2000);
                },
                error: function (xhr, status, error){
                    // console.log("Error code: " + xhr.status);
                    $(".is_subscribe").html('<p style="color: red;">' + xhr.responseJSON.error.title + '</p>');
                    setTimeout(function() {
                        $(".is_subscribe").fadeOut();
                    }, 2000);
                }
            });
        });
    });
    $(document).ready(function(){
        $('.mark-all-read').click(function(e){
            e.preventDefault();
            let button = $(this);
            $.ajax({
                url : " <?php echo e(route('mark_all_as_read')); ?>",
                method : 'POST',
                data:{
                    '_token' : "<?php echo e(csrf_token()); ?>",
                },
                // success: function(response) {
                //     $('.notify_status').each(function() {
                //         if ($(this).find('.mark-as-read').length) {
                //             $(this).fadeOut(300, function() {
                //                 $(this).remove();
                //             });
                //         }
                //     });
                //     updateNotificationCount();
                // },
                success: function(response) {
                    $('.message-center').load(location.href + ' .message-center > *');
                },
                error :function (xhr){
                    console.log(xhr);
                }
            })
        })
    })
</script>

</body>

</html>
<?php /**PATH C:\xampp\htdocs\mrdoall\resources\views/website/layout/master.blade.php ENDPATH**/ ?>